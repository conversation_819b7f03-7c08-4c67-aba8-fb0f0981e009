import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Platform,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import { ArrowLeft, Crown, Sparkles } from 'lucide-react-native';
import { usePremiumStore } from '@/stores/premiumStore';
import { PREMIUM_FEATURES } from '@/types/premium';
import PremiumSkeleton from '@/components/premium/PremiumSkeleton';
import AnimatedSubscriptionCard from '@/components/premium/AnimatedSubscriptionCard';
import PremiumFeatureShowcase from '@/components/premium/PremiumFeatureShowcase';
import PaymentProcessingModal from '@/components/premium/PaymentProcessingModal';

const { width } = Dimensions.get('window');

export default function PremiumScreen() {
  const router = useRouter();
  const {
    availablePlans,
    selectedPlan,
    isPremium,
    subscription,
    isLoading,
    isLoadingPlans,
    isProcessingPayment,
    error,
    loadPlans,
    loadSubscription,
    selectPlan,
    subscribe,
    restorePurchases,
    clearError,
  } = usePremiumStore();

  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [paymentError, setPaymentError] = useState(false);

  useEffect(() => {
    loadPlans();
    loadSubscription();
  }, []);

  const handlePlanSelect = (planId: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    selectPlan(planId);
  };

  const handleSubscribe = async () => {
    if (!selectedPlan) return;

    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    setShowPaymentModal(true);
    setPaymentSuccess(false);
    setPaymentError(false);

    try {
      const success = await subscribe(selectedPlan);
      if (success) {
        setPaymentSuccess(true);
        if (Platform.OS !== 'web') {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        }
      } else {
        setPaymentError(true);
        if (Platform.OS !== 'web') {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        }
      }
    } catch (error) {
      setPaymentError(true);
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      }
    }
  };

    Alert.alert(
      'Premium Subscription',
      'In a production app, this would integrate with your payment processor (Stripe, RevenueCat, etc.) to handle subscriptions.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Continue', 
          onPress: () => {
            Alert.alert('Success!', 'Premium features activated! (Demo)');
          }
        },
      ]
    );
  };

  const handleRestorePurchases = () => {
    Alert.alert(
      'Restore Purchases',
      'Checking for existing purchases...',
      [{ text: 'OK' }]
    );
  };

  const FeatureCard = ({ feature, index }: { feature: typeof PREMIUM_FEATURES[0]; index: number }) => (
    <View style={[styles.featureCard, feature.highlight && styles.highlightFeature]}>
      <View style={[styles.featureIcon, feature.highlight && styles.highlightIcon]}>
        <feature.icon 
          size={24} 
          color={feature.highlight ? '#FFD700' : theme.colors.primary} 
        />
      </View>
      <View style={styles.featureContent}>
        <Text style={[styles.featureTitle, feature.highlight && styles.highlightTitle]}>
          {feature.title}
        </Text>
        <Text style={styles.featureDescription}>{feature.description}</Text>
      </View>
      {feature.highlight && (
        <View style={styles.highlightBadge}>
          <Sparkles size={16} color="#FFD700" />
        </View>
      )}
    </View>
  );

  const PlanCard = ({ plan }: { plan: typeof SUBSCRIPTION_PLANS[0] }) => (
    <TouchableOpacity
      style={[
        styles.planCard,
        selectedPlan === plan.id && styles.selectedPlan,
        plan.popular && styles.popularPlan,
      ]}
      onPress={() => handlePlanSelect(plan.id)}
    >
      {plan.popular && (
        <View style={styles.popularBadge}>
          <Text style={styles.popularBadgeText}>Most Popular</Text>
        </View>
      )}
      
      <Text style={[styles.planDuration, selectedPlan === plan.id && styles.selectedText]}>
        {plan.duration}
      </Text>
      <Text style={[styles.planPrice, selectedPlan === plan.id && styles.selectedText]}>
        {plan.price}
      </Text>
      <Text style={[styles.planPricePerMonth, selectedPlan === plan.id && styles.selectedSubtext]}>
        {plan.pricePerMonth}
      </Text>
      
      {plan.savings && (
        <View style={styles.savingsBadge}>
          <Text style={styles.savingsText}>{plan.savings}</Text>
        </View>
      )}
      
      {selectedPlan === plan.id && (
        <View style={styles.selectedIndicator}>
          <CheckCircle size={20} color="white" />
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Premium Features</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Hero Section */}
        <LinearGradient
          colors={['#8B5CF6', '#EC4899']}
          style={styles.heroSection}
        >
          <View style={styles.crownContainer}>
            <Crown size={48} color="#FFD700" />
          </View>
          <Text style={styles.heroTitle}>Unlock Premium</Text>
          <Text style={styles.heroSubtitle}>
            Get unlimited access to all premium features and find your perfect match faster
          </Text>
        </LinearGradient>

        {/* Features Section */}
        <View style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Premium Features</Text>
          <View style={styles.featuresContainer}>
            {PREMIUM_FEATURES.map((feature, index) => (
              <FeatureCard key={index} feature={feature} index={index} />
            ))}
          </View>
        </View>

        {/* Subscription Plans */}
        <View style={styles.plansSection}>
          <Text style={styles.sectionTitle}>Choose Your Plan</Text>
          <View style={styles.plansContainer}>
            {SUBSCRIPTION_PLANS.map((plan) => (
              <PlanCard key={plan.id} plan={plan} />
            ))}
          </View>
        </View>

        {/* Benefits Summary */}
        <View style={styles.benefitsSection}>
          <Text style={styles.benefitsTitle}>Why Go Premium?</Text>
          <View style={styles.benefitsList}>
            <View style={styles.benefitItem}>
              <CheckCircle size={20} color={theme.colors.success} />
              <Text style={styles.benefitText}>10x more matches on average</Text>
            </View>
            <View style={styles.benefitItem}>
              <CheckCircle size={20} color={theme.colors.success} />
              <Text style={styles.benefitText}>See who likes you before swiping</Text>
            </View>
            <View style={styles.benefitItem}>
              <CheckCircle size={20} color={theme.colors.success} />
              <Text style={styles.benefitText}>Priority support and features</Text>
            </View>
            <View style={styles.benefitItem}>
              <CheckCircle size={20} color={theme.colors.success} />
              <Text style={styles.benefitText}>Cancel anytime, no commitment</Text>
            </View>
          </View>
        </View>

        {/* Subscribe Button */}
        <View style={styles.subscribeSection}>
          <TouchableOpacity style={styles.subscribeButton} onPress={handleSubscribe}>
            <LinearGradient
              colors={['#8B5CF6', '#EC4899']}
              style={styles.subscribeGradient}
            >
              <Crown size={20} color="white" />
              <Text style={styles.subscribeButtonText}>
                Start Premium - {SUBSCRIPTION_PLANS.find(p => p.id === selectedPlan)?.price}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.restoreButton} onPress={handleRestorePurchases}>
            <Text style={styles.restoreButtonText}>Restore Purchases</Text>
          </TouchableOpacity>
        </View>

        {/* Terms */}
        <View style={styles.termsSection}>
          <Text style={styles.termsText}>
            Subscription automatically renews unless auto-renew is turned off at least 24 hours before the end of the current period.
          </Text>
          <View style={styles.termsLinks}>
            <TouchableOpacity>
              <Text style={styles.termsLink}>Terms of Service</Text>
            </TouchableOpacity>
            <Text style={styles.termsSeparator}>•</Text>
            <TouchableOpacity>
              <Text style={styles.termsLink}>Privacy Policy</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#8B5CF6',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  heroSection: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  crownContainer: {
    marginBottom: 20,
  },
  heroTitle: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: 'white',
    marginBottom: 12,
    textAlign: 'center',
  },
  heroSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 24,
  },
  featuresSection: {
    backgroundColor: 'white',
    paddingVertical: 30,
  },
  sectionTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: 24,
  },
  featuresContainer: {
    paddingHorizontal: 20,
    gap: 16,
  },
  featureCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.gray50,
    padding: 20,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  highlightFeature: {
    backgroundColor: '#FFD700' + '20',
    borderColor: '#FFD700' + '40',
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  highlightIcon: {
    backgroundColor: '#FFD700' + '30',
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  highlightTitle: {
    color: '#B8860B',
  },
  featureDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    lineHeight: 20,
  },
  highlightBadge: {
    marginLeft: 12,
  },
  plansSection: {
    backgroundColor: theme.colors.gray50,
    paddingVertical: 30,
  },
  plansContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 12,
  },
  planCard: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: theme.colors.gray200,
    position: 'relative',
  },
  selectedPlan: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary,
  },
  popularPlan: {
    borderColor: '#FFD700',
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    backgroundColor: '#FFD700',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularBadgeText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  planDuration: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  planPrice: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  planPricePerMonth: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    marginBottom: 12,
  },
  selectedText: {
    color: 'white',
  },
  selectedSubtext: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  savingsBadge: {
    backgroundColor: theme.colors.success,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  savingsText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  selectedIndicator: {
    position: 'absolute',
    top: 12,
    right: 12,
  },
  benefitsSection: {
    backgroundColor: 'white',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  benefitsTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 20,
    textAlign: 'center',
  },
  benefitsList: {
    gap: 16,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  benefitText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
    flex: 1,
  },
  subscribeSection: {
    paddingHorizontal: 20,
    paddingVertical: 30,
    backgroundColor: 'white',
  },
  subscribeButton: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
  },
  subscribeGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    gap: 8,
  },
  subscribeButtonText: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  restoreButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  restoreButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: theme.colors.primary,
  },
  termsSection: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: theme.colors.gray50,
  },
  termsText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    textAlign: 'center',
    lineHeight: 18,
    marginBottom: 12,
  },
  termsLinks: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  termsLink: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: theme.colors.primary,
  },
  termsSeparator: {
    fontSize: 12,
    color: theme.colors.gray400,
  },
});
