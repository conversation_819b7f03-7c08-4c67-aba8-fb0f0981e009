import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  SafeAreaView,
  Image,
  ScrollView,
  Alert,
  Platform,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedGestureHandler,
  withSpring,
  withTiming,
  runOnJS,
  interpolate,
} from 'react-native-reanimated';
import { PanGestureHandler, PanGestureHandlerGestureEvent } from 'react-native-gesture-handler';
import * as Haptics from 'expo-haptics';
import { 
  Heart, 
  X, 
  Star, 
  MapPin, 
  Info,
  Zap,
  RotateCcw,
  Shield,
  Briefcase,
  GraduationCap
} from 'lucide-react-native';
import { theme } from '@/constants/theme';
import { mockUsers, User } from '@/data/mockData';
import { likesService } from '@/services/likesService';
import MatchModal from '@/components/modals/MatchModal';

const { width, height } = Dimensions.get('window');
const CARD_WIDTH = width - 32;
const CARD_HEIGHT = height * 0.7;
const SWIPE_THRESHOLD = width * 0.3;

// Helper function for safe haptic feedback (only works on native platforms)
const triggerHaptic = {
  light: () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } else {
      console.log('🔊 Haptic feedback: Light impact (web - no haptic)');
    }
  },
  medium: () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    } else {
      console.log('🔊 Haptic feedback: Medium impact (web - no haptic)');
    }
  },
  heavy: () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    } else {
      console.log('🔊 Haptic feedback: Heavy impact (web - no haptic)');
    }
  },
  success: () => {
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } else {
      console.log('🔊 Haptic feedback: Success notification (web - no haptic)');
    }
  },
  warning: () => {
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
    } else {
      console.log('🔊 Haptic feedback: Warning notification (web - no haptic)');
    }
  },
  error: () => {
    if (Platform.OS !== 'web') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } else {
      console.log('🔊 Haptic feedback: Error notification (web - no haptic)');
    }
  },
};

interface SwipeCardProps {
  user: User;
  index: number;
  onSwipe: (direction: 'left' | 'right' | 'up', user: User) => void;
  isTop: boolean;
}

function SwipeCard({ user, index, onSwipe, isTop }: SwipeCardProps) {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const rotate = useSharedValue(0);
  const scale = useSharedValue(isTop ? 1 : 0.95);
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);

  const gestureHandler = useAnimatedGestureHandler<PanGestureHandlerGestureEvent>({
    onStart: () => {
      if (!isTop) return;
      runOnJS(triggerHaptic.light)();
    },
    onActive: (event) => {
      if (!isTop) return;
      translateX.value = event.translationX;
      translateY.value = event.translationY;
      rotate.value = interpolate(
        event.translationX,
        [-width, 0, width],
        [-30, 0, 30]
      );
    },
    onEnd: (event) => {
      if (!isTop) return;
      
      const shouldSwipeLeft = event.translationX < -SWIPE_THRESHOLD;
      const shouldSwipeRight = event.translationX > SWIPE_THRESHOLD;
      const shouldSuperLike = event.translationY < -SWIPE_THRESHOLD;

      if (shouldSwipeLeft) {
        translateX.value = withTiming(-width * 1.5);
        runOnJS(onSwipe)('left', user);
        runOnJS(triggerHaptic.medium)();
      } else if (shouldSwipeRight) {
        translateX.value = withTiming(width * 1.5);
        runOnJS(onSwipe)('right', user);
        runOnJS(triggerHaptic.medium)();
      } else if (shouldSuperLike) {
        translateY.value = withTiming(-height);
        runOnJS(onSwipe)('up', user);
        runOnJS(triggerHaptic.heavy)();
      } else {
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
        rotate.value = withSpring(0);
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { rotate: `${rotate.value}deg` },
      { scale: scale.value },
    ],
    opacity: interpolate(
      Math.abs(translateX.value) + Math.abs(translateY.value),
      [0, width],
      [1, 0.8]
    ),
  }));

  const likeOpacity = useAnimatedStyle(() => ({
    opacity: interpolate(translateX.value, [0, width * 0.3], [0, 1]),
  }));

  const passOpacity = useAnimatedStyle(() => ({
    opacity: interpolate(translateX.value, [-width * 0.3, 0], [1, 0]),
  }));

  const superLikeOpacity = useAnimatedStyle(() => ({
    opacity: interpolate(translateY.value, [-height * 0.2, 0], [1, 0]),
  }));

  const nextPhoto = () => {
    if (currentPhotoIndex < user.photos.length - 1) {
      setCurrentPhotoIndex(currentPhotoIndex + 1);
    }
  };

  const prevPhoto = () => {
    if (currentPhotoIndex > 0) {
      setCurrentPhotoIndex(currentPhotoIndex - 1);
    }
  };

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View style={[styles.card, animatedStyle, { zIndex: -index }]}>
        {/* Swipe Indicators */}
        <Animated.View style={[styles.swipeIndicator, styles.likeIndicator, likeOpacity]}>
          <Heart size={60} color={theme.colors.like} fill={theme.colors.like} />
          <Text style={[styles.swipeText, { color: theme.colors.like }]}>LIKE</Text>
        </Animated.View>
        
        <Animated.View style={[styles.swipeIndicator, styles.passIndicator, passOpacity]}>
          <X size={60} color={theme.colors.pass} />
          <Text style={[styles.swipeText, { color: theme.colors.pass }]}>PASS</Text>
        </Animated.View>
        
        <Animated.View style={[styles.swipeIndicator, styles.superLikeIndicator, superLikeOpacity]}>
          <Star size={60} color={theme.colors.superLike} fill={theme.colors.superLike} />
          <Text style={[styles.swipeText, { color: theme.colors.superLike }]}>SUPER LIKE</Text>
        </Animated.View>

        {/* Photo Navigation Areas */}
        <TouchableOpacity style={styles.photoNavLeft} onPress={prevPhoto} activeOpacity={1} />
        <TouchableOpacity style={styles.photoNavRight} onPress={nextPhoto} activeOpacity={1} />

        {/* Main Photo */}
        <Image source={{ uri: user.photos[currentPhotoIndex] }} style={styles.photo} />
        
        {/* Photo Indicators */}
        <View style={styles.photoIndicators}>
          {user.photos.map((_, i) => (
            <View
              key={i}
              style={[
                styles.photoIndicator,
                i === currentPhotoIndex && styles.activePhotoIndicator,
              ]}
            />
          ))}
        </View>

        {/* Gradient Overlay */}
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.8)']}
          style={styles.gradient}
        />

        {/* User Info */}
        <View style={styles.userInfo}>
          <View style={styles.nameRow}>
            <Text style={styles.name}>
              {user.name}, {user.age}
            </Text>
            {user.verified && (
              <Shield size={20} color={theme.colors.info} fill={theme.colors.info} />
            )}
          </View>
          
          <View style={styles.locationRow}>
            <MapPin size={16} color={theme.colors.white} />
            <Text style={styles.location}>{user.distance} km away</Text>
          </View>
          
          <Text style={styles.bio} numberOfLines={2}>
            {user.bio}
          </Text>
          
          <View style={styles.quickInfo}>
            <View style={styles.infoItem}>
              <Briefcase size={14} color={theme.colors.white} />
              <Text style={styles.infoText}>{user.occupation}</Text>
            </View>
            <View style={styles.infoItem}>
              <GraduationCap size={14} color={theme.colors.white} />
              <Text style={styles.infoText}>{user.education}</Text>
            </View>
          </View>
        </View>
      </Animated.View>
    </PanGestureHandler>
  );
}

export default function DiscoverTab() {
  const router = useRouter();
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const [showMatch, setShowMatch] = useState(false);
  const [matchedUser, setMatchedUser] = useState<User | null>(null);

  const handleSwipe = async (direction: 'left' | 'right' | 'up', user: User) => {
    console.log(`Swiped ${direction} on ${user.name}`);

    try {
      if (direction === 'left') {
        // Send pass
        await likesService.sendPass(user.id);
      } else if (direction === 'right') {
        // Send like
        const result = await likesService.sendLike(user.id, 'like');
        if (result.isMatch) {
          setMatchedUser(user);
          setShowMatch(true);
          triggerHaptic.success();
        }
      } else if (direction === 'up') {
        // Send super like
        const result = await likesService.sendLike(user.id, 'superlike');
        if (result.isMatch) {
          setMatchedUser(user);
          setShowMatch(true);
          triggerHaptic.success();
        }
      }
    } catch (error) {
      console.error('Error handling swipe:', error);
    }

    // Move to next user
    setTimeout(() => {
      setCurrentIndex(prev => prev + 1);
    }, 300);
  };

  const handleAction = (action: 'pass' | 'like' | 'superlike') => {
    const currentUser = users[currentIndex];
    if (!currentUser) return;

    // Add haptic feedback for button press
    triggerHaptic.light();

    switch (action) {
      case 'pass':
        handleSwipe('left', currentUser);
        break;
      case 'like':
        handleSwipe('right', currentUser);
        break;
      case 'superlike':
        handleSwipe('up', currentUser);
        break;
    }
  };

  const loadMoreUsers = () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setUsers(prev => [...prev, ...mockUsers]);
      setLoading(false);
    }, 1000);
  };

  const currentUser = users[currentIndex];
  const nextUser = users[currentIndex + 1];

  if (currentIndex >= users.length) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.emptyState}>
          <Zap size={80} color={theme.colors.gray400} />
          <Text style={styles.emptyTitle}>No more profiles!</Text>
          <Text style={styles.emptySubtitle}>
            Check back later for new people in your area
          </Text>
          <TouchableOpacity style={styles.reloadButton} onPress={loadMoreUsers}>
            <Text style={styles.reloadButtonText}>Load More</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.headerButton}>
          <Info size={24} color={theme.colors.gray600} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Discover</Text>
        <TouchableOpacity style={styles.headerButton}>
          <RotateCcw size={24} color={theme.colors.gray600} />
        </TouchableOpacity>
      </View>

      {/* Cards Stack */}
      <View style={styles.cardStack}>
        {nextUser && (
          <SwipeCard
            user={nextUser}
            index={1}
            onSwipe={handleSwipe}
            isTop={false}
          />
        )}
        {currentUser && (
          <SwipeCard
            user={currentUser}
            index={0}
            onSwipe={handleSwipe}
            isTop={true}
          />
        )}
      </View>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.actionButton, styles.passButton]}
          onPress={() => handleAction('pass')}
        >
          <X size={28} color={theme.colors.pass} />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.superLikeButton]}
          onPress={() => handleAction('superlike')}
        >
          <Star size={24} color={theme.colors.superLike} />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.likeButton]}
          onPress={() => handleAction('like')}
        >
          <Heart size={28} color={theme.colors.like} />
        </TouchableOpacity>
      </View>

      {/* Match Modal */}
      <MatchModal
        visible={showMatch}
        onClose={() => setShowMatch(false)}
        onMessage={() => {
          setShowMatch(false);
          // Navigate to messages tab and then to specific chat
          if (matchedUser) {
            router.push('/(tabs)/messages');
            // Small delay to ensure navigation completes
            setTimeout(() => {
              router.push(`/chat/${matchedUser.id}`);
            }, 100);
          }
        }}
        onKeepSwiping={() => setShowMatch(false)}
        matchedUserPhoto={matchedUser?.photos[0]}
        matchedUserName={matchedUser?.name}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.gray50,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.gray100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: theme.fontSize.xl,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.gray900,
  },
  cardStack: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
  },
  card: {
    width: CARD_WIDTH,
    height: CARD_HEIGHT,
    borderRadius: theme.borderRadius.xxl,
    backgroundColor: theme.colors.white,
    position: 'absolute',
    ...theme.shadows.xl,
    overflow: 'hidden',
  },
  photo: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  photoNavLeft: {
    position: 'absolute',
    left: 0,
    top: 0,
    width: '50%',
    height: '60%',
    zIndex: 2,
  },
  photoNavRight: {
    position: 'absolute',
    right: 0,
    top: 0,
    width: '50%',
    height: '60%',
    zIndex: 2,
  },
  photoIndicators: {
    position: 'absolute',
    top: theme.spacing.md,
    left: theme.spacing.md,
    right: theme.spacing.md,
    flexDirection: 'row',
    gap: theme.spacing.xs,
    zIndex: 3,
  },
  photoIndicator: {
    flex: 1,
    height: 3,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 2,
  },
  activePhotoIndicator: {
    backgroundColor: theme.colors.white,
  },
  gradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '50%',
  },
  userInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: theme.spacing.lg,
    zIndex: 3,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
  },
  name: {
    fontSize: theme.fontSize.xxxl,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.white,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
    marginBottom: theme.spacing.sm,
  },
  location: {
    fontSize: theme.fontSize.base,
    color: theme.colors.white,
    opacity: 0.9,
  },
  bio: {
    fontSize: theme.fontSize.base,
    color: theme.colors.white,
    lineHeight: 22,
    marginBottom: theme.spacing.md,
  },
  quickInfo: {
    gap: theme.spacing.xs,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  infoText: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.white,
    opacity: 0.9,
  },

  // Swipe Indicators
  swipeIndicator: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -50 }, { translateY: -50 }],
    alignItems: 'center',
    zIndex: 4,
  },
  likeIndicator: {
    transform: [{ translateX: -100 }, { translateY: -50 }, { rotate: '-30deg' }],
  },
  passIndicator: {
    transform: [{ translateX: 0 }, { translateY: -50 }, { rotate: '30deg' }],
  },
  superLikeIndicator: {
    transform: [{ translateX: -50 }, { translateY: -100 }],
  },
  swipeText: {
    fontSize: theme.fontSize.xl,
    fontWeight: theme.fontWeight.extrabold,
    marginTop: theme.spacing.sm,
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },

  // Action Buttons
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.lg,
    gap: theme.spacing.lg,
    backgroundColor: theme.colors.white,
  },
  actionButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.md,
  },
  passButton: {
    backgroundColor: theme.colors.white,
    borderWidth: 2,
    borderColor: theme.colors.pass,
  },
  likeButton: {
    backgroundColor: theme.colors.white,
    borderWidth: 2,
    borderColor: theme.colors.like,
  },
  superLikeButton: {
    backgroundColor: theme.colors.white,
    borderWidth: 2,
    borderColor: theme.colors.superLike,
    width: 48,
    height: 48,
    borderRadius: 24,
  },

  // Empty State
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xl,
  },
  emptyTitle: {
    fontSize: theme.fontSize.xxxl,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.gray900,
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.sm,
  },
  emptySubtitle: {
    fontSize: theme.fontSize.lg,
    color: theme.colors.gray600,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
  },
  reloadButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
  },
  reloadButtonText: {
    color: theme.colors.white,
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold,
  },

  // Match Modal
  matchModal: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  matchBackground: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xl,
  },
  matchTitle: {
    fontSize: theme.fontSize.xxxxl,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.white,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  matchSubtitle: {
    fontSize: theme.fontSize.lg,
    color: theme.colors.white,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
    opacity: 0.9,
  },
  matchPhotos: {
    marginBottom: theme.spacing.xl,
  },
  matchPhoto: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
    borderColor: theme.colors.white,
  },
  matchActions: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  matchButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    borderWidth: 1,
    borderColor: theme.colors.white,
  },
  matchButtonText: {
    color: theme.colors.white,
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold,
  },
  messageButton: {
    backgroundColor: theme.colors.white,
  },
  messageButtonText: {
    color: theme.colors.primary,
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold,
  },
});
