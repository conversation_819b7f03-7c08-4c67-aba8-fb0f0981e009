import React, { useEffect } from 'react';
import { View, Text, Modal, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withRepeat,
  interpolate,
  runOnJS,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { Crown, Check, X, CreditCard, Shield, Sparkles } from 'lucide-react-native';
import { theme } from '@/constants/theme';
import { SubscriptionPlan } from '@/types/premium';

const { width, height } = Dimensions.get('window');

interface PaymentProcessingModalProps {
  visible: boolean;
  plan: SubscriptionPlan | null;
  isProcessing: boolean;
  isSuccess: boolean;
  isError: boolean;
  errorMessage?: string;
  onClose: () => void;
}

export default function PaymentProcessingModal({
  visible,
  plan,
  isProcessing,
  isSuccess,
  isError,
  errorMessage,
  onClose,
}: PaymentProcessingModalProps) {
  const backdropOpacity = useSharedValue(0);
  const modalScale = useSharedValue(0.8);
  const modalOpacity = useSharedValue(0);
  const processingRotation = useSharedValue(0);
  const successScale = useSharedValue(0);
  const errorShake = useSharedValue(0);
  const sparkleOpacity = useSharedValue(0);

  useEffect(() => {
    if (visible) {
      backdropOpacity.value = withTiming(1, { duration: 300 });
      modalScale.value = withSpring(1, { damping: 15, stiffness: 300 });
      modalOpacity.value = withTiming(1, { duration: 300 });
    } else {
      backdropOpacity.value = withTiming(0, { duration: 200 });
      modalScale.value = withTiming(0.8, { duration: 200 });
      modalOpacity.value = withTiming(0, { duration: 200 });
    }
  }, [visible]);

  useEffect(() => {
    if (isProcessing) {
      processingRotation.value = withRepeat(
        withTiming(360, { duration: 2000 }),
        -1,
        false
      );
    } else {
      processingRotation.value = withTiming(0, { duration: 300 });
    }
  }, [isProcessing]);

  useEffect(() => {
    if (isSuccess) {
      successScale.value = withSpring(1, { damping: 12, stiffness: 300 });
      sparkleOpacity.value = withTiming(1, { duration: 500 });
      
      // Auto close after success animation
      setTimeout(() => {
        runOnJS(onClose)();
      }, 2000);
    } else {
      successScale.value = withTiming(0, { duration: 200 });
      sparkleOpacity.value = withTiming(0, { duration: 200 });
    }
  }, [isSuccess]);

  useEffect(() => {
    if (isError) {
      errorShake.value = withRepeat(
        withTiming(10, { duration: 50 }),
        6,
        true,
        () => {
          errorShake.value = withTiming(0, { duration: 100 });
        }
      );
    }
  }, [isError]);

  const backdropStyle = useAnimatedStyle(() => {
    return {
      opacity: backdropOpacity.value,
    };
  });

  const modalStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: modalScale.value },
        { translateX: errorShake.value },
      ],
      opacity: modalOpacity.value,
    };
  });

  const processingStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${processingRotation.value}deg` }],
    };
  });

  const successStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: successScale.value }],
    };
  });

  const sparkleStyle = useAnimatedStyle(() => {
    return {
      opacity: sparkleOpacity.value,
    };
  });

  const renderProcessingState = () => (
    <View style={styles.stateContainer}>
      <Animated.View style={[styles.processingIcon, processingStyle]}>
        <LinearGradient
          colors={['#8B5CF6', '#EC4899']}
          style={styles.processingGradient}
        >
          <CreditCard size={32} color="white" />
        </LinearGradient>
      </Animated.View>
      
      <Text style={styles.stateTitle}>Processing Payment</Text>
      <Text style={styles.stateDescription}>
        Please wait while we process your payment securely...
      </Text>
      
      <View style={styles.securityInfo}>
        <Shield size={16} color={theme.colors.success} />
        <Text style={styles.securityText}>256-bit SSL encryption</Text>
      </View>
    </View>
  );

  const renderSuccessState = () => (
    <View style={styles.stateContainer}>
      <Animated.View style={[styles.successIcon, successStyle]}>
        <LinearGradient
          colors={['#10B981', '#059669']}
          style={styles.successGradient}
        >
          <Check size={32} color="white" />
        </LinearGradient>
      </Animated.View>
      
      {/* Success Sparkles */}
      <Animated.View style={[styles.sparklesContainer, sparkleStyle]}>
        {[...Array(6)].map((_, index) => (
          <Animated.View
            key={index}
            style={[
              styles.sparkle,
              {
                left: `${20 + (index * 12)}%`,
                top: `${30 + (index % 2) * 20}%`,
              },
            ]}
          >
            <Sparkles size={12} color="#FFD700" />
          </Animated.View>
        ))}
      </Animated.View>
      
      <Text style={styles.stateTitle}>Welcome to Premium!</Text>
      <Text style={styles.stateDescription}>
        Your subscription has been activated successfully. Enjoy all premium features!
      </Text>
      
      <View style={styles.premiumBadge}>
        <Crown size={16} color="#FFD700" />
        <Text style={styles.premiumBadgeText}>Premium Active</Text>
      </View>
    </View>
  );

  const renderErrorState = () => (
    <View style={styles.stateContainer}>
      <View style={styles.errorIcon}>
        <LinearGradient
          colors={['#EF4444', '#DC2626']}
          style={styles.errorGradient}
        >
          <X size={32} color="white" />
        </LinearGradient>
      </View>
      
      <Text style={styles.stateTitle}>Payment Failed</Text>
      <Text style={styles.stateDescription}>
        {errorMessage || 'There was an issue processing your payment. Please try again.'}
      </Text>
      
      <View style={styles.errorActions}>
        <Text style={styles.retryText}>Tap outside to try again</Text>
      </View>
    </View>
  );

  const getCurrentState = () => {
    if (isProcessing) return renderProcessingState();
    if (isSuccess) return renderSuccessState();
    if (isError) return renderErrorState();
    return renderProcessingState();
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={isProcessing ? undefined : onClose}
    >
      <View style={styles.container}>
        <Animated.View style={[styles.backdrop, backdropStyle]} />
        
        <Animated.View style={[styles.modal, modalStyle]}>
          <LinearGradient
            colors={['#FFFFFF', '#F8FAFC']}
            style={styles.modalGradient}
          >
            {/* Plan Info */}
            {plan && (
              <View style={styles.planInfo}>
                <Text style={styles.planName}>{plan.name} Plan</Text>
                <Text style={styles.planPrice}>${plan.price.toFixed(2)}</Text>
              </View>
            )}
            
            {/* Current State */}
            {getCurrentState()}
          </LinearGradient>
        </Animated.View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modal: {
    width: width * 0.85,
    maxWidth: 400,
    borderRadius: theme.borderRadius.xl,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 20,
  },
  modalGradient: {
    padding: theme.spacing.xl,
  },
  planInfo: {
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
    paddingBottom: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
  },
  planName: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.text,
    marginBottom: 4,
  },
  planPrice: {
    fontSize: theme.fontSize.xl,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.primary,
  },
  stateContainer: {
    alignItems: 'center',
  },
  processingIcon: {
    marginBottom: theme.spacing.lg,
  },
  processingGradient: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  successIcon: {
    marginBottom: theme.spacing.lg,
  },
  successGradient: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorIcon: {
    marginBottom: theme.spacing.lg,
  },
  errorGradient: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sparklesContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  sparkle: {
    position: 'absolute',
  },
  stateTitle: {
    fontSize: theme.fontSize.xl,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  stateDescription: {
    fontSize: theme.fontSize.base,
    color: theme.colors.gray[600],
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: theme.spacing.lg,
  },
  securityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
    backgroundColor: theme.colors.success + '10',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  },
  securityText: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.success,
    fontWeight: theme.fontWeight.medium,
  },
  premiumBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
    backgroundColor: '#FFD700' + '20',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  },
  premiumBadgeText: {
    fontSize: theme.fontSize.sm,
    color: '#B45309',
    fontWeight: theme.fontWeight.bold,
  },
  errorActions: {
    alignItems: 'center',
  },
  retryText: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.gray[500],
    fontStyle: 'italic',
  },
});
