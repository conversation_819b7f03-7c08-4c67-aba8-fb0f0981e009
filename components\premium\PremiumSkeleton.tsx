import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withRepeat, 
  withTiming,
  interpolate,
  useEffect
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { theme } from '@/constants/theme';

const { width } = Dimensions.get('window');

interface PremiumSkeletonProps {
  variant?: 'plans' | 'features' | 'subscription' | 'full';
}

export default function PremiumSkeleton({ variant = 'full' }: PremiumSkeletonProps) {
  const shimmerTranslate = useSharedValue(-width);

  useEffect(() => {
    shimmerTranslate.value = withRepeat(
      withTiming(width, { duration: 1500 }),
      -1,
      false
    );
  }, []);

  const shimmerStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: shimmerTranslate.value }],
    };
  });

  const SkeletonBox = ({ 
    width: boxWidth, 
    height, 
    borderRadius = theme.borderRadius.md,
    style = {} 
  }: {
    width: number | string;
    height: number;
    borderRadius?: number;
    style?: any;
  }) => (
    <View style={[
      {
        width: boxWidth,
        height,
        borderRadius,
        backgroundColor: theme.colors.gray[200],
        overflow: 'hidden',
      },
      style
    ]}>
      <Animated.View style={[styles.shimmer, shimmerStyle]}>
        <LinearGradient
          colors={['transparent', 'rgba(255,255,255,0.4)', 'transparent']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.shimmerGradient}
        />
      </Animated.View>
    </View>
  );

  const renderPlansSkeleton = () => (
    <View style={styles.plansContainer}>
      {[1, 2, 3].map((index) => (
        <View key={index} style={styles.planCard}>
          <SkeletonBox width="100%" height={20} style={{ marginBottom: 8 }} />
          <SkeletonBox width="60%" height={32} style={{ marginBottom: 4 }} />
          <SkeletonBox width="80%" height={16} style={{ marginBottom: 12 }} />
          <SkeletonBox width="40%" height={24} borderRadius={12} />
        </View>
      ))}
    </View>
  );

  const renderFeaturesSkeleton = () => (
    <View style={styles.featuresContainer}>
      {[1, 2, 3, 4, 5, 6].map((index) => (
        <View key={index} style={styles.featureCard}>
          <SkeletonBox width={40} height={40} borderRadius={20} />
          <View style={styles.featureContent}>
            <SkeletonBox width="70%" height={18} style={{ marginBottom: 4 }} />
            <SkeletonBox width="90%" height={14} />
          </View>
        </View>
      ))}
    </View>
  );

  const renderSubscriptionSkeleton = () => (
    <View style={styles.subscriptionContainer}>
      <View style={styles.subscriptionHeader}>
        <SkeletonBox width={60} height={60} borderRadius={30} />
        <View style={styles.subscriptionInfo}>
          <SkeletonBox width="60%" height={20} style={{ marginBottom: 4 }} />
          <SkeletonBox width="40%" height={16} />
        </View>
      </View>
      <View style={styles.subscriptionDetails}>
        <SkeletonBox width="100%" height={16} style={{ marginBottom: 8 }} />
        <SkeletonBox width="80%" height={16} style={{ marginBottom: 8 }} />
        <SkeletonBox width="60%" height={16} />
      </View>
    </View>
  );

  const renderFullSkeleton = () => (
    <View style={styles.container}>
      {/* Header Skeleton */}
      <View style={styles.header}>
        <SkeletonBox width={60} height={60} borderRadius={30} style={{ marginBottom: 16 }} />
        <SkeletonBox width="60%" height={24} style={{ marginBottom: 8 }} />
        <SkeletonBox width="80%" height={16} />
      </View>

      {/* Features Skeleton */}
      <View style={styles.section}>
        <SkeletonBox width="40%" height={20} style={{ marginBottom: 16 }} />
        {renderFeaturesSkeleton()}
      </View>

      {/* Plans Skeleton */}
      <View style={styles.section}>
        <SkeletonBox width="50%" height={20} style={{ marginBottom: 16 }} />
        {renderPlansSkeleton()}
      </View>

      {/* Button Skeleton */}
      <View style={styles.buttonContainer}>
        <SkeletonBox width="100%" height={56} borderRadius={28} />
      </View>
    </View>
  );

  switch (variant) {
    case 'plans':
      return renderPlansSkeleton();
    case 'features':
      return renderFeaturesSkeleton();
    case 'subscription':
      return renderSubscriptionSkeleton();
    default:
      return renderFullSkeleton();
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: theme.spacing.md,
  },
  header: {
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
  },
  section: {
    marginBottom: theme.spacing.xl,
  },
  plansContainer: {
    gap: theme.spacing.md,
  },
  planCard: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    borderWidth: 1,
    borderColor: theme.colors.gray[200],
  },
  featuresContainer: {
    gap: theme.spacing.md,
  },
  featureCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.gray[200],
  },
  featureContent: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  subscriptionContainer: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    borderWidth: 1,
    borderColor: theme.colors.gray[200],
  },
  subscriptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  subscriptionInfo: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  subscriptionDetails: {
    gap: theme.spacing.sm,
  },
  buttonContainer: {
    marginTop: 'auto',
    paddingTop: theme.spacing.lg,
  },
  shimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  shimmerGradient: {
    flex: 1,
    width: width,
  },
});
